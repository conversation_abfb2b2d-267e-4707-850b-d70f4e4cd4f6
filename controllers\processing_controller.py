"""
Processing Controller - Xử lý xử lý ảnh và tạo AI
Handle image processing and AI generation
"""

from flask import Blueprint, jsonify, request, redirect, url_for, render_template  # Flask components
import json  # Thư viện xử lý JSON
import traceback  # Thư viện theo dõi lỗi
from pathlib import Path  # Thư viện xử lý đường dẫn
from datetime import datetime  # Thư viện ngày tháng

from gemini_ocr_service import GeminiOCRService  # OCR Service chính
from ai_generator import AIImageGenerator  # AI Generator chính
from models.session_model import SessionModel  # Model session
from ai_config import get_gemini_config  # Cấu hình Gemini


class ProcessingController:
    """Controller xử lý ảnh và tạo AI - Controller for image processing and AI generation"""

    def __init__(self):
        """Khởi tạo Processing Controller"""
        self.blueprint = Blueprint('processing', __name__)  # Tạo Flask Blueprint
        self.setup_routes()  # Thiết lập routes
        self.setup_models()  # Thiết lập models
        # print("⚙️ Processing Controller initialized")

    def setup_routes(self):
        """Thiết lập các routes xử lý - Setup processing routes"""
        # Route xử lý ảnh chính
        self.blueprint.add_url_rule('/process_images', 'process_images', self.process_images, methods=['POST'])

    def setup_models(self):
        """Khởi tạo các model xử lý và AI pipeline - Initialize processing models and AI pipeline"""
        self.ocr_service = GeminiOCRService()  # Khởi tạo OCR service chính
        self.ai_generator = AIImageGenerator()  # Khởi tạo AI generator chính
        self.session_model = SessionModel()  # Khởi tạo model session

        # Khởi tạo two-stage AI pipeline - Initialize the two-stage AI pipeline
        try:
            from services.ai_pipeline_service import AIProcessingPipeline  # Import AI pipeline service
            self.ai_pipeline = AIProcessingPipeline()  # Tạo instance AI pipeline
            print("✅ Two-Stage AI Pipeline initialized in Processing Controller")
        except Exception as e:
            print(f"⚠️ AI Pipeline initialization failed: {e}")
            self.ai_pipeline = None  # Đặt None nếu khởi tạo thất bại

    def process_images(self):
        """Xử lý ảnh đã chụp và tạo ảnh AI - Process captured images and generate AI image"""
        try:
            # Import camera controller để lấy session chung - Import camera controller to get shared session
            from controllers.camera_controller import camera_controller

            # Sử dụng session từ camera controller (instance chung) - Use session from camera controller (shared instance) (COMMENTED FOR TESTING)
            # if not camera_controller.session_model.current_session:  # Nếu không có session hiện tại
            #     return jsonify({
            #         'status': 'error',  # Trạng thái lỗi
            #         'message': 'No active session found. Please capture images first.'  # Thông báo lỗi
            #     }), 400  # HTTP 400 Bad Request

            # session = camera_controller.session_model.current_session  # Lấy session hiện tại
            # Tạo session giả cho testing - Create fake session for testing
            session = {
                'session_id': 'test_session_' + str(datetime.now().timestamp()),
                'card_captured': True,
                'face_captured': True,
                'status': 'ready'
            }
            print(f"📁 Using session: {session.get('session_id')}")  # Log session ID
            print(f"📁 Session status: card={session.get('card_captured')}, face={session.get('face_captured')}")  # Log trạng thái chụp ảnh

            # Kiểm tra đã chụp đủ ảnh chưa - Check if both images are captured (COMMENTED FOR TESTING)
            # if not session.get('card_captured') or not session.get('face_captured'):
            #     return jsonify({
            #         'status': 'error',  # Trạng thái lỗi
            #         'message': 'Please capture both business card and face images first.'  # Thông báo cần chụp đủ ảnh
            #     }), 400  # HTTP 400 Bad Request

            # Lấy template prompt từ request - Get prompt template from request
            prompt_template = 'prompt'  # Mặc định - default
            try:
                if request.is_json and request.get_json():  # Nếu request có JSON data
                    data = request.get_json()  # Lấy JSON data
                    prompt_template = data.get('prompt_template', 'prompt')  # Lấy template hoặc dùng mặc định
                    print(f"🎨 Selected prompt template: {prompt_template}")  # Log template được chọn
                else:
                    print("🎨 Using default prompt template (no JSON data received)")  # Log dùng template mặc định
            except Exception as e:
                print(f"⚠️ Error parsing request data: {e}, using default prompt")  # Log lỗi parse request

            # Sử dụng cấu hình AI với prompt đã chọn - Use AI configuration with selected prompt
            ai_config = get_gemini_config()  # Lấy cấu hình Gemini
            ai_config['prompt_template'] = prompt_template  # Đặt template prompt
            print(f"🤖 Using AI config: Model={ai_config['model']}, Template={prompt_template}")  # Log cấu hình AI

            # Cập nhật trạng thái session sử dụng session của camera controller - Update session status using camera controller's session (COMMENTED FOR TESTING)
            # camera_controller.session_model.update_session(status='processing', ai_config=ai_config)  # Đặt trạng thái đang xử lý
            print("🧪 Testing mode: Skipping session update")

            # Lấy đường dẫn ảnh cần thiết - Get required image paths
            card_image_path = r"D:\DEV_AI_Gen\duy\trunk\test_images\card_sample.png"  # Đường dẫn ảnh name card
            face_image_path = r"D:\DEV_AI_Gen\duy\trunk\test_images\face_sample.png"  # Đường dẫn ảnh khuôn mặt

            # Xác thực đầu vào - Validate inputs (COMMENTED FOR TESTING)
            # if not card_image_path or not Path(card_image_path).exists():  # Nếu không có ảnh card hoặc file không tồn tại
            #     return jsonify({
            #         'status': 'error',  # Trạng thái lỗi
            #         'message': 'Business card image not found'  # Thông báo không tìm thấy ảnh card
            #     }), 500  # HTTP 500 Internal Server Error

            # if not face_image_path or not Path(face_image_path).exists():  # Nếu không có ảnh face hoặc file không tồn tại
            #     return jsonify({
            #         'status': 'error',  # Trạng thái lỗi
            #         'message': 'Face image not found'  # Thông báo không tìm thấy ảnh face
            #     }), 500  # HTTP 500 Internal Server Error

            # Thực thi Two-Stage AI Pipeline - Execute Two-Stage AI Pipeline
            try:
                if self.ai_pipeline:  # Nếu có AI pipeline
                    print("🚀 Using Two-Stage AI Pipeline")  # Log sử dụng pipeline

                    # Thực thi pipeline hoàn chỉnh - Execute the complete pipeline
                    pipeline_result = self.ai_pipeline.process_business_card(
                        card_image_path=card_image_path,  # Đường dẫn ảnh card
                        face_image_path=face_image_path,  # Đường dẫn ảnh face
                        prompt_template=prompt_template,  # Template prompt
                        session_id=session.get('session_id')  # Session ID
                    )

                    if pipeline_result['success']:  # Nếu pipeline thành công
                        # Trích xuất kết quả từ pipeline - Extract results from pipeline
                        card_info = pipeline_result['stage1_ocr']['data']  # Dữ liệu OCR từ giai đoạn 1
                        generation_result = pipeline_result['stage2_generation']['data']  # Kết quả tạo ảnh từ giai đoạn 2

                        print(f"✅ Two-Stage Pipeline Completed Successfully!")  # Log pipeline hoàn thành
                        print(f"   Total Time: {pipeline_result['total_processing_time']}")  # Log tổng thời gian
                        print(f"   Stage 1 (OCR): {pipeline_result['stage1_ocr']['processing_time']}")  # Log thời gian OCR
                        print(f"   Stage 2 (Generation): {pipeline_result['stage2_generation']['processing_time']}")  # Log thời gian tạo ảnh

                        # Lưu thông tin card vào session - Save card info to session (COMMENTED FOR TESTING)
                        # camera_controller.session_model.save_card_info(card_info)  # Gọi hàm lưu thông tin card
                        print("🧪 Testing mode: Skipping card info save")

                    else:  # Nếu pipeline thất bại
                        print(f"❌ Two-Stage Pipeline Failed: {pipeline_result['error']}")  # Log pipeline thất bại
                        # camera_controller.session_model.update_session(  # Cập nhật session với lỗi (COMMENTED FOR TESTING)
                        #     status='error',  # Trạng thái lỗi
                        #     error=pipeline_result['error']  # Thông tin lỗi
                        # )
                        print("🧪 Testing mode: Skipping session error update")
                        return jsonify({
                            'status': 'error',  # Trạng thái lỗi
                            'message': f'AI Pipeline failed: {pipeline_result["error"]}',  # Thông báo lỗi
                            'details': pipeline_result.get('details', '')  # Chi tiết lỗi
                        }), 500  # HTTP 500 Internal Server Error

                else:  # Nếu không có AI pipeline
                    # Fallback sang xử lý legacy - Fallback to legacy processing
                    print("⚠️ Using Legacy Processing (AI Pipeline not available)")  # Log sử dụng legacy

                    # Xử lý OCR legacy - Legacy OCR processing
                    if not hasattr(self, 'ocr_service'):  # Nếu chưa có OCR service
                        from gemini_ocr_service import GeminiOCRService  # Import OCR service
                        self.ocr_service = GeminiOCRService()  # Tạo instance OCR service

                    card_info = self.ocr_service.extract_text_from_card(card_image_path)  # Trích xuất thông tin từ card
                    if not card_info:  # Nếu không trích xuất được
                        raise ValueError("OCR extraction failed")  # Ném lỗi

                    # camera_controller.session_model.save_card_info(card_info)  # Lưu thông tin card (COMMENTED FOR TESTING)
                    print("🧪 Testing mode: Skipping legacy card info save")

                    # Tạo ảnh AI legacy - Legacy AI generation
                    if not hasattr(self, 'ai_generator'):  # Nếu chưa có AI generator
                        from ai_generator import AIImageGenerator  # Import AI generator
                        self.ai_generator = AIImageGenerator()  # Tạo instance AI generator

                    generation_result = self.ai_generator.generate_dollhouse_image(  # Tạo ảnh dollhouse
                        face_image_path,  # Đường dẫn ảnh khuôn mặt
                        card_info,  # Thông tin card
                        ai_config  # Cấu hình AI
                    )

                # Xử lý cả kết quả pipeline và legacy - Handle both pipeline and legacy results
                if generation_result and generation_result.get('success'):  # Nếu tạo ảnh thành công
                    # Trích xuất ảnh đã tạo từ kết quả - Extract generated images from result
                    generated_images = []  # Danh sách ảnh đã tạo

                    # Xử lý định dạng kết quả pipeline - Handle pipeline result format
                    if 'generated_images' in generation_result:  # Nếu có key 'generated_images'
                        generated_images = generation_result['generated_images']  # Lấy danh sách ảnh
                    # Xử lý định dạng kết quả legacy - Handle legacy result format
                    elif 'image_paths' in generation_result:  # Nếu có key 'image_paths'
                        generated_images = generation_result['image_paths']  # Lấy danh sách đường dẫn
                    elif 'image_path' in generation_result:  # Nếu có key 'image_path'
                        generated_images = [generation_result['image_path']]  # Tạo danh sách với 1 ảnh

                    print(f"✅ AI image generated: {len(generated_images)} images")  # Log số ảnh đã tạo

                    # Thêm tất cả ảnh đã tạo vào session - Add all generated images to session
                    session_images = []  # Danh sách ảnh session
                    for i, img_path in enumerate(generated_images):
                        try:
                            # session_image_path = camera_controller.session_model.add_generated_image(  # COMMENTED FOR TESTING
                            #     img_path,
                            #     f"AI Generated - {prompt_template.title()} (Variant {i+1})"
                            # )
                            session_images.append(img_path)
                            print(f"✅ Added generated image {i+1}: {img_path} (Testing mode)")
                        except Exception as img_error:
                            print(f"⚠️ Error adding image {i+1}: {img_error}")
                            continue

                    if session_images:
                        # Update session status to completed (COMMENTED FOR TESTING)
                        # camera_controller.session_model.update_session(
                        #     status='completed',
                        #     completed_at=datetime.now().isoformat()
                        # )

                        print(f"✅ Session updated to completed status (Testing mode)")
                        # print(f"📊 Session data: {camera_controller.session_model.current_session.get('status')}")
                        # print(f"📊 Generated images count: {len(camera_controller.session_model.current_session.get('generated_images', []))}")
                        print(f"📊 Generated images count: {len(session_images)} (Testing mode)")

                        return jsonify({
                            'status': 'success',
                            'card_info': card_info,
                            'generated_images': session_images,
                            'message': f'Xử lý thành công với {generation_result.get("api_used", "AI")}!'
                        })
                    else:
                        raise ValueError("No images were successfully added to session")
                else:
                    error_msg = generation_result.get('error', 'Unknown error') if generation_result else 'No result returned'
                    print(f"❌ AI generation failed: {error_msg}")
                    
                    # Update session status to error (COMMENTED FOR TESTING)
                    # camera_controller.session_model.update_session(
                    #     status='error',
                    #     error=error_msg
                    # )
                    print("🧪 Testing mode: Skipping session error update")

                    return jsonify({
                        'status': 'error',
                        'message': f'AI image generation failed: {error_msg}'
                    }), 500
            except Exception as ai_error:
                print(f"❌ AI generation error: {ai_error}")
                # Update session status to error (COMMENTED FOR TESTING)
                # camera_controller.session_model.update_session(
                #     status='error',
                #     error=str(ai_error)
                # )
                print("🧪 Testing mode: Skipping session error update")
                return jsonify({
                    'status': 'error',
                    'message': f'AI generation failed: {ai_error}'
                }), 500
        
        except Exception as e:
            print(f"❌ Processing error: {e}")
            print("📊 Full error traceback:")
            traceback.print_exc()

            error_message = f'Processing failed: {str(e)}'

            # Update session with error
            try:
                if self.session_model.current_session:
                    self.session_model.update_session(
                        status='error',
                        error=error_message,
                        error_at=datetime.now().isoformat()
                    )
            except Exception as session_error:
                print(f"⚠️ Session update error: {session_error}")

            return jsonify({
                'status': 'error',
                'message': error_message,
                'details': 'Check server logs for full error details'
            }), 500
    

    
    def _get_relative_path(self, absolute_path):
        """Convert absolute path to relative path for web serving"""
        if not absolute_path:
            return None
        
        path = Path(absolute_path)
        
        # If path is already relative to static, return as is
        if 'static' in path.parts:
            static_index = path.parts.index('static')
            return str(Path(*path.parts[static_index:]))
        
        # Otherwise, assume it's in static/img
        return f"static/img/{path.name}"


# Create global processing controller instance
processing_controller = ProcessingController()

def get_processing_blueprint():
    """Get processing controller blueprint"""
    return processing_controller.blueprint
